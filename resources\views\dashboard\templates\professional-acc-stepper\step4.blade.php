<div class="form-card">
    <div class="row">
        <div class="col-12 mb-3">
            <h2 class="fs-title">Add your opening hours</h2>
            <p>Set standard opening hours to show on your profile page, these hours do not impact your calendar
                availability</p>
        </div>

        <div class="gray-card mb-10">
            @php
                $daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                $userOpeningHours = auth()->user()->openingHours->keyBy('day');
            @endphp

            @foreach ($daysOfWeek as $index => $day)
                @php
                    $openingHour = $userOpeningHours->get($day); // Get the user's opening hour for the current day
$isChecked = $openingHour && $openingHour->type === 'open'; // Check if the day is marked as "open"
$start = $isChecked ? $openingHour->open : ''; // Get the start time if open
$end = $isChecked ? $openingHour->close : ''; // Get the end time if open
                @endphp
                <div class="time-picker-calendar2 mb-8">
                    <label class="days">
                        <input type="checkbox" value="{{ $day }}" name="availability[{{ $index }}][day]"
                            {{ $isChecked ? 'checked' : '' }}>
                        <span class="checkmark">{{ $day }}</span>
                    </label>
                    <div class="time-picker-range2">
                        <div class="checked-time">
                            <div class="start-time1">
                                <input type="text" name="availability[{{ $index }}][start]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="Select Time"
                                    value="{{ $start }}">
                            </div>
                            <p class="mb-0"> - </p>
                            <div class="end-time1">
                                <input type="text" name="availability[{{ $index }}][end]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="Select Time"
                                    value="{{ $end }}">
                            </div>
                        </div>

                        <div class="closed-time">
                            <p> Closed</p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="d-flex justify-content-between">
            <p>List of holidays</p>
            <label class="days holiday-list"> <input type="checkbox" class="select-all">
                <span class="checkmark">Select All</span>
            </label>
        </div>

        <div class="gray-card  mb-8">
            @foreach ($holidays as $index => $holiday)
                @php
                    $checked_date = auth()->user()->holidays->pluck('holiday_id')->contains($holiday->id);
                @endphp

                <div class="time-picker-calendar">
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days"><input type="checkbox" name="holidays[{{ $index }}][holiday_id]"
                                value="{{ $holiday->id }}" class="day-checkbox" {{ $checked_date ? 'checked' : '' }}>
                            <span class="checkmark ">{{ $holiday->name ?? '' }}</span>
                            <input type="hidden" name="holidays[{{ $index }}][name]"
                                value="{{ $holiday->name }}">
                            <input type="hidden" name="holidays[{{ $index }}][date]"
                                value="{{ $holiday->date }}">
                        </label>
                        <p>{{ $holiday->date ?? '' }}</p>
                    </div>
                    <div class="start-time " style="display: {{ $checked_date == true ? 'block' : '' }};">
                        <div class="d-flex gap-10 justify-content-center">
                            <input type="text" class="flatpickr-time no_validate"
                                name="holidays[{{ $index }}][start_time]" placeholder="Select Time">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate"
                                name="holidays[{{ $index }}][end_time]" placeholder="Select Time">
                        </div>
                    </div>
                </div>
            @endforeach


            {{-- Custom holidays --}}
            @forelse (auth()->user()->customHolidays as $customHoliday)
                <div class="time-picker-calendar custom-holiday-div">
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days">
                            <input type="checkbox" class="day-checkbox" name="custom_holidays[{{ $loop->index }}]" {{ isset($customHoliday->start_time, $customHoliday->end_time) ? 'checked' : '' }}>
                            <span class="checkmark">{{ $customHoliday->name }}</span>
                            <input type="hidden" name="custom_holidays[{{ $loop->index }}][name]" value="{{ $customHoliday->name }}">
                            <input type="hidden" name="custom_holidays[{{ $loop->index }}][date]" value="{{ $customHoliday->date }}">
                        </label>
                        <p>{{ $customHoliday->date }}</p>
                    </div>
                    <button class="delete-holiday btn btn-sm btn-danger mb-8">Delete</button>
                    <div class="start-time" style="display: {{ isset($customHoliday->start_time, $customHoliday->end_time) ? 'block' : 'none' }};">
                        <div class="d-flex gap-10">
                            <input type="text" class="flatpickr-time no_validate flatpickr-input"
                                placeholder="Select Time" name="custom_holidays[{{ $loop->index }}][start_time]" value="{{ $customHoliday->start_time }}">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate flatpickr-input"
                                placeholder="Select Time" name="custom_holidays[{{ $loop->index }}][end_time]" value="{{ $customHoliday->end_time }}">
                        </div>
                    </div>
                </div>
            @empty
                
            @endforelse
            {{-- Custom holidays end --}}

            <button type="button" class="add-custom-holiday-btn">+ Add Custom Holiday</button>
            <!-- Modal -->
            <div id="customHolidayModal" class="HolidayModal modal" style="display:none;">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h4 class="text-center mb-7">Add Custom Holiday</h4>
                    <label>Holiday Name:</label>
                    <input class="no_validate" type="text" id="customHolidayName" placeholder="Enter holiday name">
                    <label>Date:</label>
                    <input class="no_validate" type="text" id="customHolidayDate" placeholder="Select date">
                    <button type="button" class="blue-btn py-3" id="saveCustomHoliday">Save</button>
                </div>
            </div>
        </div>
        <p>Select holidays to show your availability. </p>
    </div>
</div>
